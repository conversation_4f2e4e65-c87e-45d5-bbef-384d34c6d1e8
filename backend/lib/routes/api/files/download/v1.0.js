const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  const findFile = async (next) => {
    try {
      const fileId = req.params.fileId;

      if (!fileId) {
        return next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'File ID is required'
        });
      }

      const FileModel = require('../../../../models/file');
      const file = await FileModel.findOne({
        _id: fileId,
        isDeleted: false
      }).lean();

      if (!file) {
        return next({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: 'File not found'
        });
      }

      next(null, file);
    } catch (error) {
      logger.logError(['files/download/findFile', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  const getFileUrl = async (file, next) => {
    try {
      const cacheKey = cacheService.getTelegramUrlKey(file.telegramFileId);

      // Check cache first
      let fileUrl = await cacheService.get(cacheKey);

      if (!fileUrl) {
        // Get fresh URL from Telegram
        fileUrl = await telegramService.getFileUrl(file.telegramFileId);

        // Cache URL for 30 minutes (Telegram URLs expire after ~1 hour)
        await cacheService.set(cacheKey, fileUrl, 1800);
      }

      next(null, file, fileUrl);
    } catch (error) {
      logger.logError(['files/download/getFileUrl', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to get download URL'
      });
    }
  };

  const streamFile = async (file, fileUrl, next) => {
    try {
      // Set response headers
      res.setHeader('Content-Type', file.mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.originalFileName)}"`);
      res.setHeader('Content-Length', file.fileSize);

      // Get file stream from Telegram
      const fileStream = await telegramService.getFileStream(file.telegramFileId);

      // Pipe the stream to response
      fileStream.pipe(res);

      // Handle stream events
      fileStream.on('error', (error) => {
        logger.logError(['files/download/streamFile', error], __dirname);
        if (!res.headersSent) {
          res.status(500).json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Failed to download file'
          });
        }
      });

      fileStream.on('end', () => {
        logger.logInfo(`File downloaded: ${file.originalFileName}, ID: ${file._id}`);
      });

      // Don't call next() here as we're streaming the response
    } catch (error) {
      logger.logError(['files/download/streamFile', error], __dirname);
      if (!res.headersSent) {
        res.status(500).json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Failed to download file'
        });
      }
    }
  };

  async.waterfall([findFile, getFileUrl, streamFile], (err, data) => {
    if (err && !res.headersSent) {
      if (_.isError(err)) {
        data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        };
      }
      res.json(data || err);
    }
  });
};
