const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');
const logger = require('../../../../logger');

module.exports = (req, res) => {
  const validateParams = (next) => {
    try {
      const { folderName, parentId } = req.body;

      if (!folderName || typeof folderName !== 'string' || folderName.trim().length === 0) {
        return next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Folder name is required and must be a non-empty string'
        });
      }

      // Validate folder name (no special characters that could cause issues)
      const invalidChars = /[<>:"/\\|?*]/;
      if (invalidChars.test(folderName.trim())) {
        return next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Folder name contains invalid characters'
        });
      }

      // Validate parent folder if provided
      if (parentId) {
        const FolderModel = require('../../../../models/folder');
        FolderModel.findOne({
          _id: parentId,
          isDeleted: false
        }).lean()
        .then(parentFolder => {
          if (!parentFolder) {
            return next({
              code: CONSTANTS.CODE.INVALID_PARAMS,
              message: 'Parent folder not found'
            });
          }
          next(null, folderName.trim(), parentId || null);
        })
        .catch(error => {
          logger.logError(['folders/create/validateParams', error], __dirname);
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        });
      } else {
        next(null, folderName.trim(), parentId || null);
      }
    } catch (error) {
      logger.logError(['folders/create/validateParams', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  const checkDuplicate = (folderName, parentId, next) => {
    try {
      const FolderModel = require('../../../../models/folder');

      FolderModel.findOne({
        folderName: folderName,
        parentId: parentId,
        isDeleted: false
      }).lean()
      .then(existingFolder => {
        if (existingFolder) {
          return next({
            code: CONSTANTS.CODE.INVALID_PARAMS,
            message: 'A folder with this name already exists in the current location'
          });
        }
        next(null, folderName, parentId);
      })
      .catch(error => {
        logger.logError(['folders/create/checkDuplicate', error], __dirname);
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      });
    } catch (error) {
      logger.logError(['folders/create/checkDuplicate', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  const createFolder = (folderName, parentId, next) => {
    try {
      const FolderModel = require('../../../../models/folder');

      const newFolder = new FolderModel({
        folderName: folderName,
        parentId: parentId,
        createdAt: new Date()
      });

      newFolder.save()
        .then(savedFolder => {
          logger.logInfo(`Folder created successfully: ${folderName}, ID: ${savedFolder._id}`);

          // Send response immediately
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              id: savedFolder._id,
              folderName: savedFolder.folderName,
              parentId: savedFolder.parentId,
              createdAt: savedFolder.createdAt
            },
            message: 'Folder created successfully'
          });

          // Clear cache asynchronously (don't wait for it)
          Promise.all([
            cacheService.clearFolderCache(parentId),
            cacheService.clearSearchCache()
          ]).catch(error => {
            logger.logError(['folders/create/clearCache', error], __dirname);
          });
        })
        .catch(error => {
          logger.logError(['folders/create/createFolder', error], __dirname);

          // Handle duplicate key error (in case of race condition)
          if (error.code === 11000) {
            next({
              code: CONSTANTS.CODE.INVALID_PARAMS,
              message: 'A folder with this name already exists in the current location'
            });
          } else {
            next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
        });
    } catch (error) {
      logger.logError(['folders/create/createFolder', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  async.waterfall([validateParams, checkDuplicate, createFolder], (err, data) => {
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      };
    }

    res.json(data || err);
  });
};
