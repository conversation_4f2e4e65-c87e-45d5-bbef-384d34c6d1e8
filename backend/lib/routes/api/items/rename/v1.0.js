const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  const validateParams = async (next) => {
    try {
      const { id } = req.params;
      const { newName } = req.body;

      if (!id) {
        return next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Item ID is required'
        });
      }

      if (!newName || typeof newName !== 'string' || newName.trim().length === 0) {
        return next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'New name is required and must be a non-empty string'
        });
      }

      // Validate name (no special characters for folders)
      const invalidChars = /[<>:"/\\|?*]/;
      if (invalidChars.test(newName.trim())) {
        return next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Name contains invalid characters'
        });
      }

      next(null, id, newName.trim());
    } catch (error) {
      logger.logError(['items/rename/validateParams', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  const findAndRenameItem = async (id, newName, next) => {
    try {
      const FileModel = require('../../../../models/file');
      const FolderModel = require('../../../../models/folder');

      // Try to find as file first
      let item = await FileModel.findOne({
        _id: id,
        isDeleted: false
      });

      let itemType = 'file';
      let updateResult;

      if (item) {
        // It's a file - update originalFileName
        updateResult = await FileModel.updateOne(
          { _id: id, isDeleted: false },
          { originalFileName: newName }
        );
      } else {
        // Try to find as folder
        item = await FolderModel.findOne({
          _id: id,
          isDeleted: false
        });

        if (!item) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: 'Item not found'
          });
        }

        itemType = 'folder';

        // Check for duplicate folder name in same parent
        const duplicateFolder = await FolderModel.findOne({
          folderName: newName,
          parentId: item.parentId,
          isDeleted: false,
          _id: { $ne: id }
        });

        if (duplicateFolder) {
          return next({
            code: CONSTANTS.CODE.INVALID_PARAMS,
            message: 'A folder with this name already exists in the current location'
          });
        }

        // Update folder name
        updateResult = await FolderModel.updateOne(
          { _id: id, isDeleted: false },
          { folderName: newName }
        );
      }

      if (updateResult.matchedCount === 0) {
        return next({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: 'Item not found'
        });
      }

      // Clear cache for parent folder
      await cacheService.clearFolderCache(item.parentId);
      await cacheService.clearSearchCache();

      logger.logInfo(`${itemType} renamed successfully: ${item[itemType === 'file' ? 'originalFileName' : 'folderName']} -> ${newName}, ID: ${id}`);

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          id: id,
          newName: newName,
          type: itemType,
          parentId: item.parentId
        },
        message: `${itemType === 'file' ? 'File' : 'Folder'} renamed successfully`
      });

    } catch (error) {
      logger.logError(['items/rename/findAndRenameItem', error], __dirname);
      
      // Handle duplicate key error for folders
      if (error.code === 11000) {
        next({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'A folder with this name already exists in the current location'
        });
      } else {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }
    }
  };

  async.waterfall([validateParams, findAndRenameItem], (err, data) => {
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      };
    }

    res.json(data || err);
  });
};
