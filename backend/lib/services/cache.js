const redisConnections = require('../connections/redis');

class CacheService {
  constructor() {
    this.redis = redisConnections('master');
    this.defaultTTL = 900; // 15 minutes
  }

  /**
   * Generate cache key for folder content
   * @param {string} folderId - Folder ID (null for root)
   * @returns {string} - Cache key
   */
  getFolderContentKey(folderId) {
    return `folder_content:${folderId || 'root'}`;
  }

  /**
   * Generate cache key for search results
   * @param {string} query - Search query
   * @returns {string} - Cache key
   */
  getSearchKey(query) {
    return `search:${Buffer.from(query).toString('base64')}`;
  }

  /**
   * Generate cache key for Telegram file URL
   * @param {string} telegramFileId - Telegram file ID
   * @returns {string} - Cache key
   */
  getTelegramUrlKey(telegramFileId) {
    return `telegram_url:${telegramFileId}`;
  }

  /**
   * Get cached data
   * @param {string} key - Cache key
   * @returns {Promise<any>} - Cached data or null
   */
  async get(key) {
    try {
      if (!this.redis) {
        return null;
      }

      const data = await this.redis.getConnection().get(key);
      if (!data) {
        return null;
      }

      return JSON.parse(data);
    } catch (error) {
      global.logger.logInfo(['CacheService.get error', error.message], __dirname);
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Set cached data
   * @param {string} key - Cache key
   * @param {any} data - Data to cache
   * @param {number} ttl - TTL in seconds (optional)
   * @returns {Promise<boolean>} - Success status
   */
  async set(key, data, ttl = this.defaultTTL) {
    try {
      if (!this.redis) {
        return false;
      }

      const serializedData = JSON.stringify(data);
      await this.redis.getConnection().setex(key, ttl, serializedData);
      return true;
    } catch (error) {
      global.logger.logInfo(['CacheService.set error', error.message], __dirname);
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete cached data
   * @param {string} key - Cache key
   * @returns {Promise<boolean>} - Success status
   */
  async del(key) {
    try {
      if (!this.redis) {
        return false;
      }

      await this.redis.getConnection().del(key);
      return true;
    } catch (error) {
      global.logger.logInfo(['CacheService.del error', error.message], __dirname);
      console.error('Cache del error:', error);
      return false;
    }
  }

  /**
   * Delete multiple cache keys
   * @param {string[]} keys - Array of cache keys
   * @returns {Promise<boolean>} - Success status
   */
  async delMultiple(keys) {
    try {
      if (!this.redis || !keys.length) {
        return false;
      }

      await this.redis.getConnection().del(...keys);
      return true;
    } catch (error) {
      global.logger.logInfo(['CacheService.delMultiple error', error.message], __dirname);
      console.error('Cache delMultiple error:', error);
      return false;
    }
  }

  /**
   * Clear folder content cache and parent folders
   * @param {string} folderId - Folder ID
   * @returns {Promise<void>}
   */
  async clearFolderCache(folderId) {
    try {
      const keys = [this.getFolderContentKey(folderId)];

      // Also clear parent folder cache if this is a subfolder
      if (folderId) {
        // Find parent folder and clear its cache too
        const FolderModel = require('../models/folder');
        const folder = await FolderModel.findById(folderId).lean();
        if (folder && folder.parentId) {
          keys.push(this.getFolderContentKey(folder.parentId.toString()));
        } else if (folder) {
          // Clear root folder cache
          keys.push(this.getFolderContentKey(null));
        }
      }

      await this.delMultiple(keys);
    } catch (error) {
      global.logger.logInfo(['CacheService.clearFolderCache error', error.message], __dirname);
      console.error('Cache clearFolderCache error:', error);
    }
  }

  /**
   * Clear search cache (all search results)
   * @returns {Promise<void>}
   */
  async clearSearchCache() {
    try {
      if (!this.redis) {
        return;
      }

      const keys = await this.redis.getConnection().keys('search:*');
      if (keys.length > 0) {
        await this.delMultiple(keys);
      }
    } catch (error) {
      global.logger.logInfo(['CacheService.clearSearchCache error', error.message], __dirname);
      console.error('Cache clearSearchCache error:', error);
    }
  }
}

module.exports = new CacheService();
