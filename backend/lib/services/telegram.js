// Telegram service for file storage system
const TelegramBot = require('node-telegram-bot-api');
const fs = require('fs');
const path = require('path');
const config = require('config');

class TelegramService {
  constructor() {
    this.bot = null;
    this.chatId = null;
    this.init();
  }

  init() {
    try {
      const botToken = config.get('telegram.botToken');
      const chatId = config.get('telegram.chatId');

      if (!botToken || !chatId) {
        console.error('Telegram bot token or chat ID not configured');
        return;
      }

      this.bot = new TelegramBot(botToken, { polling: false });
      this.chatId = chatId;

      console.log('Telegram service initialized successfully');
      console.log(`Bot Token: ${botToken.substring(0, 10)}...`);
      console.log(`Chat ID: ${chatId}`);
    } catch (error) {
      console.error('Failed to initialize Telegram service:', error.message);
    }
  }

  /**
   * Upload file to Telegram
   * @param {string} filePath - Path to the file to upload
   * @param {string} originalFileName - Original filename
   * @returns {Promise<string>} - Telegram file ID
   */
  async uploadFile(filePath, originalFileName) {
    try {
      if (!this.bot || !this.chatId) {
        throw new Error('Telegram service not initialized');
      }

      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      console.log(`Uploading file to Telegram: ${originalFileName}`);

      // Determine file type and upload accordingly
      const isVideo = originalFileName.toLowerCase().match(/\.(mp4|avi|mov|mkv|webm|flv|wmv)$/);
      const isImage = originalFileName.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp|webp)$/);

      let result;
      let fileId;

      if (isVideo) {
        // Upload as video
        console.log('Uploading as video...');
        result = await this.bot.sendVideo(this.chatId, filePath, {
          caption: `🎥 ${originalFileName}\n🕒 ${new Date().toISOString()}`
        });
        fileId = result.video?.file_id;
      } else if (isImage) {
        // Upload as photo
        console.log('Uploading as photo...');
        result = await this.bot.sendPhoto(this.chatId, filePath, {
          caption: `🖼️ ${originalFileName}\n🕒 ${new Date().toISOString()}`
        });
        fileId = result.photo?.[result.photo.length - 1]?.file_id; // Get highest resolution
      } else {
        // Upload as document (default)
        console.log('Uploading as document...');
        result = await this.bot.sendDocument(this.chatId, filePath, {
          caption: `📁 ${originalFileName}\n🕒 ${new Date().toISOString()}`
        });
        fileId = result.document?.file_id;
      }

      if (!fileId) {
        console.error('Telegram response:', JSON.stringify(result, null, 2));
        throw new Error('No file_id received from Telegram API');
      }

      console.log(`File uploaded successfully. Telegram file ID: ${fileId}`);
      return fileId;
    } catch (error) {
      console.error('Error uploading file to Telegram:', error.message);
      throw new Error(`Failed to upload file to Telegram: ${error.message}`);
    }
  }

  /**
   * Get file download URL from Telegram
   * @param {string} fileId - Telegram file ID
   * @returns {Promise<string>} - Download URL
   */
  async getFileUrl(fileId) {
    try {
      if (!this.bot) {
        throw new Error('Telegram service not initialized');
      }

      console.log(`Getting file URL from Telegram: ${fileId}`);

      const fileInfo = await this.bot.getFile(fileId);
      const fileUrl = `https://api.telegram.org/file/bot${this.bot.token}/${fileInfo.file_path}`;

      console.log(`File URL retrieved: ${fileUrl}`);
      return fileUrl;
    } catch (error) {
      console.error('Error getting file URL from Telegram:', error.message);
      throw new Error(`Failed to get file URL: ${error.message}`);
    }
  }

  /**
   * Download file from Telegram
   * @param {string} fileId - Telegram file ID
   * @returns {Promise<Buffer>} - File buffer
   */
  async downloadFile(fileId) {
    try {
      if (!this.bot) {
        throw new Error('Telegram service not initialized');
      }

      console.log(`Downloading file from Telegram: ${fileId}`);

      const fileUrl = await this.getFileUrl(fileId);
      const fetch = require('node-fetch');
      const response = await fetch(fileUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const buffer = await response.buffer();
      console.log(`File downloaded successfully. Size: ${buffer.length} bytes`);

      return buffer;
    } catch (error) {
      console.error('Error downloading file from Telegram:', error.message);
      throw new Error(`Failed to download file: ${error.message}`);
    }
  }

  /**
   * Get file stream from Telegram
   * @param {string} fileId - Telegram file ID
   * @returns {Promise<NodeJS.ReadableStream>} - File stream
   */
  async getFileStream(fileId) {
    try {
      if (!this.bot) {
        throw new Error('Telegram service not initialized');
      }

      console.log(`Getting file stream from Telegram: ${fileId}`);

      const fileUrl = await this.getFileUrl(fileId);
      const fetch = require('node-fetch');
      const response = await fetch(fileUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log(`File stream ready for: ${fileId}`);
      return response.body;
    } catch (error) {
      console.error('Error getting file stream from Telegram:', error.message);
      throw new Error(`Failed to get file stream: ${error.message}`);
    }
  }

  /**
   * Test Telegram connection
   * @returns {Promise<boolean>} - Connection status
   */
  async testConnection() {
    try {
      if (!this.bot) {
        return false;
      }

      const me = await this.bot.getMe();
      console.log(`Telegram bot connected: @${me.username} (${me.first_name})`);
      return true;
    } catch (error) {
      console.error('Telegram connection test failed:', error.message);
      return false;
    }
  }
}

module.exports = new TelegramService();
