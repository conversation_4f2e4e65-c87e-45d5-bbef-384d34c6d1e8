<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram File Storage - File Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .toolbar {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.1em;
        }

        .breadcrumb-item {
            color: #667eea;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .breadcrumb-item:hover {
            background: #f0f0f0;
        }

        .breadcrumb-item.current {
            background: #667eea;
            color: white;
        }

        .upload-section {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .upload-btn, .create-folder-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s;
        }

        .upload-btn:hover, .create-folder-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .content {
            padding: 30px;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .file-item {
            background: white;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .file-item:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .file-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            border-radius: 10px;
        }

        .file-thumbnail {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .folder-icon {
            background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
            color: white;
        }

        .file-icon.image {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .file-icon.text {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        }

        .file-icon.json {
            background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
        }

        .file-icon.other {
            background: linear-gradient(135deg, #c2e9fb 0%, #a1c4fd 100%);
        }

        .file-name {
            font-weight: 600;
            margin-bottom: 8px;
            word-break: break-word;
            color: #333;
        }

        .file-info {
            font-size: 0.9em;
            color: #666;
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }

        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            margin: 5% auto;
            padding: 0;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: 600;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
            max-height: 60vh;
            overflow: auto;
        }

        .preview-content {
            text-align: center;
        }

        .preview-image {
            max-width: 100%;
            max-height: 500px;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .preview-text {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: left;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow: auto;
            border: 1px solid #e9ecef;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin-top: 20px;
            transition: all 0.3s;
        }

        .download-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .file-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .file-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 Telegram File Storage</h1>
            <p>Manage your files with style and preview them instantly</p>
        </div>

        <div class="toolbar">
            <div class="breadcrumb" id="breadcrumb">
                <a href="#" class="breadcrumb-item current" onclick="loadFolder(null)">🏠 Home</a>
            </div>
            
            <div class="upload-section">
                <input type="file" id="fileInput" style="display: none;" multiple>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    📤 Upload Files
                </button>
                <button class="create-folder-btn" onclick="createFolder()">
                    📁 New Folder
                </button>
            </div>
        </div>

        <div class="content">
            <div id="loading" class="loading">Loading files...</div>
            <div id="error" class="error" style="display: none;"></div>
            <div id="fileGrid" class="file-grid"></div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-title" id="modalTitle">File Preview</span>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="previewContent" class="preview-content"></div>
                <div class="file-actions">
                    <button class="download-btn" id="downloadBtn">📥 Download</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/v1.0';
        let currentFolder = null;
        let currentFiles = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadFolder(null);
            setupFileUpload();
        });

        // Load folder content
        async function loadFolder(folderId) {
            currentFolder = folderId;
            showLoading(true);
            hideError();

            try {
                const url = folderId ? `${API_BASE}/browse/${folderId}` : `${API_BASE}/browse`;
                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 200) {
                    currentFiles = [...data.data.folders, ...data.data.files];
                    renderFiles(data.data);
                    updateBreadcrumb(folderId);
                } else {
                    showError('Failed to load folder: ' + data.message);
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // Render files and folders
        function renderFiles(data) {
            const grid = document.getElementById('fileGrid');
            grid.innerHTML = '';

            // Render folders
            data.folders.forEach(folder => {
                const item = createFileItem(folder, 'folder');
                grid.appendChild(item);
            });

            // Render files
            data.files.forEach(file => {
                const item = createFileItem(file, 'file');
                grid.appendChild(item);
            });

            if (data.folders.length === 0 && data.files.length === 0) {
                grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 50px; color: #666;">📂 This folder is empty</div>';
            }
        }

        // Create file/folder item
        function createFileItem(item, type) {
            const div = document.createElement('div');
            div.className = 'file-item';
            
            if (type === 'folder') {
                div.onclick = () => loadFolder(item.id);
                div.innerHTML = `
                    <div class="file-icon folder-icon">📁</div>
                    <div class="file-name">${item.name}</div>
                    <div class="file-info">
                        <span>Folder</span>
                        <span>${new Date(item.createdAt).toLocaleDateString()}</span>
                    </div>
                `;
            } else {
                div.onclick = () => previewFile(item);
                
                const fileType = getFileType(item.mimeType);
                const isImage = fileType === 'image';
                
                div.innerHTML = `
                    ${isImage ? 
                        `<img class="file-thumbnail" src="${API_BASE}/files/preview/${item.id}" alt="${item.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="file-icon ${fileType}" style="display: none;">${getFileIcon(fileType)}</div>` :
                        `<div class="file-icon ${fileType}">${getFileIcon(fileType)}</div>`
                    }
                    <div class="file-name">${item.name}</div>
                    <div class="file-info">
                        <span>${formatFileSize(item.size)}</span>
                        <span>${new Date(item.uploadDate).toLocaleDateString()}</span>
                    </div>
                `;
            }
            
            return div;
        }

        // Get file type from MIME type
        function getFileType(mimeType) {
            if (mimeType.startsWith('image/')) return 'image';
            if (mimeType.startsWith('text/')) return 'text';
            if (mimeType.includes('json')) return 'json';
            return 'other';
        }

        // Get file icon
        function getFileIcon(type) {
            const icons = {
                image: '🖼️',
                text: '📄',
                json: '📋',
                other: '📎'
            };
            return icons[type] || '📎';
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Preview file
        async function previewFile(file) {
            document.getElementById('modalTitle').textContent = file.name;
            const previewContent = document.getElementById('previewContent');
            const downloadBtn = document.getElementById('downloadBtn');
            
            downloadBtn.onclick = () => downloadFile(file.id, file.name);
            
            previewContent.innerHTML = '<div style="padding: 20px;">Loading preview...</div>';
            document.getElementById('previewModal').style.display = 'block';

            try {
                const fileType = getFileType(file.mimeType);
                
                if (fileType === 'image') {
                    previewContent.innerHTML = `
                        <img class="preview-image" src="${API_BASE}/files/preview/${file.id}" alt="${file.name}">
                    `;
                } else if (fileType === 'text' || fileType === 'json') {
                    const response = await fetch(`${API_BASE}/files/preview/${file.id}`);
                    const text = await response.text();
                    previewContent.innerHTML = `
                        <div class="preview-text">${text}</div>
                    `;
                } else {
                    previewContent.innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 4em; margin-bottom: 20px;">${getFileIcon(fileType)}</div>
                            <h3>${file.name}</h3>
                            <p>Size: ${formatFileSize(file.size)}</p>
                            <p>Type: ${file.mimeType}</p>
                            <p style="margin-top: 20px; color: #666;">This file type cannot be previewed in browser.</p>
                        </div>
                    `;
                }
            } catch (error) {
                previewContent.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #d63031;">
                        <h3>Preview Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Download file
        function downloadFile(fileId, fileName) {
            const link = document.createElement('a');
            link.href = `${API_BASE}/files/download/${fileId}`;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Close modal
        function closeModal() {
            document.getElementById('previewModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('previewModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Setup file upload
        function setupFileUpload() {
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', handleFileUpload);
        }

        // Handle file upload
        async function handleFileUpload(event) {
            const files = event.target.files;
            if (files.length === 0) return;

            for (let file of files) {
                await uploadFile(file);
            }
            
            // Reload current folder
            loadFolder(currentFolder);
            
            // Clear input
            event.target.value = '';
        }

        // Upload single file
        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            if (currentFolder) {
                formData.append('parentId', currentFolder);
            }

            try {
                const response = await fetch(`${API_BASE}/files/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                if (result.code !== 200) {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError(`Failed to upload ${file.name}: ${error.message}`);
            }
        }

        // Create folder
        async function createFolder() {
            const name = prompt('Enter folder name:');
            if (!name) return;

            try {
                const response = await fetch(`${API_BASE}/folders/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        folderName: name,
                        parentId: currentFolder
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    loadFolder(currentFolder);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('Failed to create folder: ' + error.message);
            }
        }

        // Update breadcrumb
        function updateBreadcrumb(folderId) {
            // Simple breadcrumb - can be enhanced to show full path
            const breadcrumb = document.getElementById('breadcrumb');
            breadcrumb.innerHTML = `
                <a href="#" class="breadcrumb-item ${!folderId ? 'current' : ''}" onclick="loadFolder(null)">🏠 Home</a>
                ${folderId ? '<span> / </span><span class="breadcrumb-item current">Current Folder</span>' : ''}
            `;
        }

        // Utility functions
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('fileGrid').style.display = show ? 'none' : 'grid';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
    </script>
</body>
</html>
