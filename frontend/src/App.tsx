import React, { useEffect, useState } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  Box,
  Container,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Alert,
  Snackbar,
  LinearProgress,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  CreateNewFolder as CreateFolderIcon,
} from '@mui/icons-material';
import { AppProvider, useApp } from './contexts/AppContext';
import Header from './components/Header';
import Breadcrumbs from './components/Breadcrumbs';
import FileGrid from './components/FileGrid';
import { fileApi } from './services/api';
import { Item, FileItem, FolderItem, BreadcrumbItem } from './types';
import { validateFileName } from './utils/helpers';

const theme = createTheme({
  palette: {
    primary: {
      main: '#667eea',
      light: '#8fa4f3',
      dark: '#4c63d2',
    },
    secondary: {
      main: '#764ba2',
      light: '#9575cd',
      dark: '#512da8',
    },
    background: {
      default: '#f8f9fa',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 700,
    },
    h6: {
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
          '&:hover': {
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
          },
        },
      },
    },
    MuiFab: {
      styleOverrides: {
        root: {
          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
        },
      },
    },
  },
});

const AppContent: React.FC = () => {
  const { state, dispatch } = useApp();
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [newFolderName, setNewFolderName] = useState('');
  const [newItemName, setNewItemName] = useState('');
  const [itemToRename, setItemToRename] = useState<Item | null>(null);
  const [itemToDelete, setItemToDelete] = useState<Item | null>(null);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Load folder content
  const loadFolderContent = async (folderId: string | null) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await fileApi.browseFolder(folderId || undefined);
      if (response.code === 200 && response.data) {
        const allItems: Item[] = [...response.data.folders, ...response.data.files];
        dispatch({ type: 'SET_ITEMS', payload: allItems });
        dispatch({ type: 'SET_CURRENT_FOLDER', payload: folderId });
      }
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to load folder content' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Build breadcrumbs (simplified - in real app, you'd track the full path)
  const buildBreadcrumbs = (folderId: string | null): BreadcrumbItem[] => {
    if (!folderId) {
      return [{ id: null, name: 'Home' }];
    }
    // In a real app, you'd build the full path by traversing parent folders
    return [
      { id: null, name: 'Home' },
      { id: folderId, name: 'Current Folder' },
    ];
  };

  // Navigate to folder
  const handleNavigate = (folderId: string | null) => {
    const breadcrumbs = buildBreadcrumbs(folderId);
    dispatch({ type: 'SET_BREADCRUMBS', payload: breadcrumbs });
    dispatch({ type: 'CLEAR_SEARCH' });
    loadFolderContent(folderId);
  };

  // Handle item click
  const handleItemClick = (item: Item) => {
    if (item.type === 'folder') {
      handleNavigate(item.id);
    }
    // For files, you could open a preview dialog
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFiles) return;

    const files = Array.from(selectedFiles);

    for (const file of files) {
      dispatch({
        type: 'ADD_UPLOAD',
        payload: { fileName: file.name, progress: 0, status: 'uploading' },
      });

      try {
        await fileApi.uploadFile(
          file,
          state.currentFolderId || undefined,
          (progress) => {
            dispatch({
              type: 'UPDATE_UPLOAD',
              payload: { fileName: file.name, progress, status: 'uploading' },
            });
          }
        );

        dispatch({
          type: 'UPDATE_UPLOAD',
          payload: { fileName: file.name, progress: 100, status: 'completed' },
        });

        // Remove upload progress after 2 seconds
        setTimeout(() => {
          dispatch({ type: 'REMOVE_UPLOAD', payload: file.name });
        }, 2000);

      } catch (error: any) {
        dispatch({
          type: 'UPDATE_UPLOAD',
          payload: { fileName: file.name, progress: 0, status: 'error' },
        });
        setSnackbar({
          open: true,
          message: `Failed to upload ${file.name}: ${error.message}`,
          severity: 'error',
        });
      }
    }

    setUploadDialogOpen(false);
    setSelectedFiles(null);

    // Reload current folder
    loadFolderContent(state.currentFolderId);
  };

  // Handle create folder
  const handleCreateFolder = async () => {
    if (!validateFileName(newFolderName)) {
      setSnackbar({
        open: true,
        message: 'Invalid folder name',
        severity: 'error',
      });
      return;
    }

    try {
      await fileApi.createFolder(newFolderName, state.currentFolderId || undefined);
      setSnackbar({
        open: true,
        message: 'Folder created successfully',
        severity: 'success',
      });
      setCreateFolderDialogOpen(false);
      setNewFolderName('');
      loadFolderContent(state.currentFolderId);
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: error.message || 'Failed to create folder',
        severity: 'error',
      });
    }
  };

  // Handle rename item
  const handleRenameItem = async () => {
    if (!itemToRename || !validateFileName(newItemName)) {
      setSnackbar({
        open: true,
        message: 'Invalid name',
        severity: 'error',
      });
      return;
    }

    try {
      await fileApi.renameItem(itemToRename.id, newItemName);
      setSnackbar({
        open: true,
        message: 'Item renamed successfully',
        severity: 'success',
      });
      setRenameDialogOpen(false);
      setItemToRename(null);
      setNewItemName('');
      loadFolderContent(state.currentFolderId);
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: error.message || 'Failed to rename item',
        severity: 'error',
      });
    }
  };

  // Handle delete item
  const handleDeleteItem = async () => {
    if (!itemToDelete) return;

    try {
      await fileApi.deleteItem(itemToDelete.id);
      setSnackbar({
        open: true,
        message: 'Item deleted successfully',
        severity: 'success',
      });
      setDeleteDialogOpen(false);
      setItemToDelete(null);
      loadFolderContent(state.currentFolderId);
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: error.message || 'Failed to delete item',
        severity: 'error',
      });
    }
  };

  // Initialize app
  useEffect(() => {
    loadFolderContent(null);
  }, []);

  const displayItems = state.searchQuery ? state.searchResults : state.items;

  return (
    <Box
      sx={{
        flexGrow: 1,
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      }}
    >
      <Header onUploadClick={() => setUploadDialogOpen(true)} />

      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box
          sx={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            borderRadius: 4,
            p: 2,
            mb: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }}
        >
          <Breadcrumbs onNavigate={handleNavigate} />
        </Box>

        {state.loading && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress
              sx={{
                borderRadius: 2,
                height: 6,
                background: 'rgba(255, 255, 255, 0.3)',
                '& .MuiLinearProgress-bar': {
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                }
              }}
            />
          </Box>
        )}

        {state.error && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: 3,
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            }}
          >
            {state.error}
          </Alert>
        )}

        <Box
          sx={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            borderRadius: 4,
            p: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            minHeight: '60vh',
          }}
        >
          <FileGrid
            items={displayItems}
            onItemClick={handleItemClick}
            onItemRename={(item) => {
              setItemToRename(item);
              setNewItemName(item.name);
              setRenameDialogOpen(true);
            }}
            onItemDelete={(item) => {
              setItemToDelete(item);
              setDeleteDialogOpen(true);
            }}
          />
        </Box>
      </Container>

      {/* Upload Progress */}
      {state.uploads.length > 0 && (
        <Box sx={{ position: 'fixed', bottom: 100, right: 20, width: 300 }}>
          {state.uploads.map((upload) => (
            <Box key={upload.fileName} sx={{ mb: 1, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 2 }}>
              <Typography variant="body2" noWrap>
                {upload.fileName}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={upload.progress}
                color={upload.status === 'error' ? 'error' : 'primary'}
                sx={{ mt: 1 }}
              />
            </Box>
          ))}
        </Box>
      )}

      {/* Floating Action Buttons */}
      <Fab
        color="primary"
        aria-label="upload"
        sx={{ position: 'fixed', bottom: 80, right: 20 }}
        onClick={() => setUploadDialogOpen(true)}
      >
        <AddIcon />
      </Fab>

      <Fab
        color="secondary"
        aria-label="create folder"
        sx={{ position: 'fixed', bottom: 20, right: 20 }}
        onClick={() => setCreateFolderDialogOpen(true)}
      >
        <CreateFolderIcon />
      </Fab>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Upload Files</DialogTitle>
        <DialogContent>
          <input
            type="file"
            multiple
            onChange={(e) => setSelectedFiles(e.target.files)}
            style={{ width: '100%', padding: '10px' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleFileUpload} disabled={!selectedFiles} variant="contained">
            Upload
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Folder Dialog */}
      <Dialog open={createFolderDialogOpen} onClose={() => setCreateFolderDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Folder</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Folder Name"
            fullWidth
            variant="outlined"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateFolderDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateFolder} variant="contained">
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Rename Dialog */}
      <Dialog open={renameDialogOpen} onClose={() => setRenameDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Rename {itemToRename?.type === 'folder' ? 'Folder' : 'File'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="New Name"
            fullWidth
            variant="outlined"
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRenameDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleRenameItem} variant="contained">
            Rename
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Delete {itemToDelete?.type === 'folder' ? 'Folder' : 'File'}</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{itemToDelete?.name}"?
            {itemToDelete?.type === 'folder' && ' This will also delete all contents inside this folder.'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteItem} variant="contained" color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppProvider>
        <AppContent />
      </AppProvider>
    </ThemeProvider>
  );
}

export default App;
