import React from 'react';
import {
  Breadcrumbs as Mui<PERSON><PERSON>crum<PERSON>,
  <PERSON>,
  Typography,
  Box,
} from '@mui/material';
import { Home as HomeIcon, NavigateNext as NavigateNextIcon } from '@mui/icons-material';
import { useApp } from '../contexts/AppContext';

interface BreadcrumbsProps {
  onNavigate: (folderId: string | null) => void;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ onNavigate }) => {
  const { state } = useApp();

  if (state.isSearching || state.searchQuery) {
    return (
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" color="primary">
          {state.isSearching ? 'Searching...' : `Search results for "${state.searchQuery}"`}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
      <MuiBreadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
      >
        {state.breadcrumbs.map((breadcrumb, index) => {
          const isLast = index === state.breadcrumbs.length - 1;
          const isHome = breadcrumb.id === null;

          if (isLast) {
            return (
              <Typography
                key={breadcrumb.id || 'home'}
                color="text.primary"
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                {isHome && <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />}
                {breadcrumb.name}
              </Typography>
            );
          }

          return (
            <Link
              key={breadcrumb.id || 'home'}
              underline="hover"
              color="inherit"
              href="#"
              onClick={(e) => {
                e.preventDefault();
                onNavigate(breadcrumb.id);
              }}
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              {isHome && <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />}
              {breadcrumb.name}
            </Link>
          );
        })}
      </MuiBreadcrumbs>
    </Box>
  );
};

export default Breadcrumbs;
