import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CardMedia,
} from '@mui/material';
import {
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as PreviewIcon,
  Close as CloseIcon,
  Image as ImageIcon,
  Description as TextIcon,
  Code as CodeIcon,
} from '@mui/icons-material';
import { Item, FileItem, FolderItem } from '../types';
import { formatFileSize, formatDate, getFileIcon, isImageFile, downloadFile } from '../utils/helpers';
import { fileApi } from '../services/api';

interface FileGridProps {
  items: Item[];
  onItemClick: (item: Item) => void;
  onItemRename: (item: Item) => void;
  onItemDelete: (item: Item) => void;
}

const FileGrid: React.FC<FileGridProps> = ({
  items,
  onItemClick,
  onItemRename,
  onItemDelete,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewItem, setPreviewItem] = useState<FileItem | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, item: Item) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedItem(item);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedItem(null);
  };

  const handleDownload = () => {
    if (selectedItem && selectedItem.type === 'file') {
      const fileItem = selectedItem as FileItem;
      const downloadUrl = fileApi.downloadFile(fileItem.id);
      downloadFile(downloadUrl, fileItem.name);
    }
    handleMenuClose();
  };

  const handleRename = () => {
    if (selectedItem) {
      onItemRename(selectedItem);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedItem) {
      onItemDelete(selectedItem);
    }
    handleMenuClose();
  };

  const handlePreview = () => {
    if (selectedItem && selectedItem.type === 'file') {
      setPreviewItem(selectedItem as FileItem);
      setPreviewOpen(true);
    }
    handleMenuClose();
  };

  const handleItemDoubleClick = (item: Item) => {
    if (item.type === 'file') {
      setPreviewItem(item as FileItem);
      setPreviewOpen(true);
    } else {
      onItemClick(item);
    }
  };

  const getFileTypeIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <ImageIcon />;
    if (mimeType.startsWith('text/') || mimeType.includes('json')) return <TextIcon />;
    if (mimeType.includes('javascript') || mimeType.includes('css')) return <CodeIcon />;
    return <FileIcon />;
  };

  const isPreviewable = (mimeType: string) => {
    return mimeType.startsWith('image/') ||
           mimeType.startsWith('text/') ||
           mimeType.includes('json') ||
           mimeType === 'application/pdf';
  };

  const renderItemIcon = (item: Item) => {
    if (item.type === 'folder') {
      return (
        <Box
          sx={{
            width: 80,
            height: 80,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: 2,
            color: 'white',
            mb: 1,
          }}
        >
          <FolderIcon sx={{ fontSize: 40 }} />
        </Box>
      );
    }

    const fileItem = item as FileItem;

    // Show thumbnail for images
    if (isImageFile(fileItem.mimeType)) {
      return (
        <Box
          sx={{
            width: 80,
            height: 80,
            borderRadius: 2,
            overflow: 'hidden',
            mb: 1,
            position: 'relative',
            border: '2px solid #f0f0f0',
          }}
        >
          <img
            src={fileApi.previewFile(fileItem.id)}
            alt={fileItem.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
            onError={(e) => {
              // Fallback to icon if image fails to load
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              target.nextElementSibling?.setAttribute('style', 'display: flex');
            }}
          />
          <Box
            sx={{
              display: 'none',
              width: '100%',
              height: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
              position: 'absolute',
              top: 0,
              left: 0,
            }}
          >
            <ImageIcon sx={{ fontSize: 32, color: 'white' }} />
          </Box>
        </Box>
      );
    }

    // Show appropriate icon for other file types
    return (
      <Box
        sx={{
          width: 80,
          height: 80,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: fileItem.mimeType.startsWith('text/') || fileItem.mimeType.includes('json')
            ? 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)'
            : 'linear-gradient(135deg, #c2e9fb 0%, #a1c4fd 100%)',
          borderRadius: 2,
          color: 'white',
          mb: 1,
        }}
      >
        {getFileTypeIcon(fileItem.mimeType)}
      </Box>
    );
  };

  const renderItemDetails = (item: Item) => {
    if (item.type === 'folder') {
      const folderItem = item as FolderItem;
      return (
        <Typography variant="caption" color="text.secondary">
          Created: {formatDate(folderItem.createdAt)}
        </Typography>
      );
    }

    const fileItem = item as FileItem;
    return (
      <Box>
        <Typography variant="caption" color="text.secondary" display="block">
          Size: {formatFileSize(fileItem.size)}
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block">
          Uploaded: {formatDate(fileItem.uploadDate)}
        </Typography>
        <Chip
          label={fileItem.mimeType}
          size="small"
          variant="outlined"
          sx={{ mt: 0.5, fontSize: '0.7rem', height: 20 }}
        />
      </Box>
    );
  };

  if (items.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 200,
          color: 'text.secondary',
        }}
      >
        <Typography variant="h6">No files or folders found</Typography>
      </Box>
    );
  }

  return (
    <>
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: 2, p: 2 }}>
        {items.map((item) => (
          <Box key={item.id}>
            <Card
              sx={{
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                },
              }}
              onClick={() => onItemClick(item)}
              onDoubleClick={() => handleItemDoubleClick(item)}
            >
              <CardContent sx={{ textAlign: 'center', pb: 1 }}>
                {renderItemIcon(item)}
                <Typography
                  variant="subtitle2"
                  sx={{
                    mt: 1,
                    fontWeight: 'medium',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                  title={item.name}
                >
                  {item.name}
                </Typography>
                {renderItemDetails(item)}
              </CardContent>
              <CardActions sx={{ justifyContent: 'flex-end', pt: 0 }}>
                <IconButton
                  size="small"
                  onClick={(e) => handleMenuOpen(e, item)}
                >
                  <MoreVertIcon />
                </IconButton>
              </CardActions>
            </Card>
          </Box>
        ))}
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedItem?.type === 'file' && isPreviewable((selectedItem as FileItem).mimeType) && (
          <MenuItem onClick={handlePreview}>
            <PreviewIcon sx={{ mr: 1 }} />
            Preview
          </MenuItem>
        )}
        {selectedItem?.type === 'file' && (
          <MenuItem onClick={handleDownload}>
            <DownloadIcon sx={{ mr: 1 }} />
            Download
          </MenuItem>
        )}
        <MenuItem onClick={handleRename}>
          <EditIcon sx={{ mr: 1 }} />
          Rename
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Preview Modal */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            minHeight: '60vh',
          }
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            py: 2,
          }}
        >
          <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
            {previewItem?.name}
          </Typography>
          <IconButton
            onClick={() => setPreviewOpen(false)}
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column', minHeight: '50vh' }}>
          {previewItem && (
            <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', p: 3 }}>
              {previewItem.mimeType.startsWith('image/') ? (
                <img
                  src={fileApi.previewFile(previewItem.id)}
                  alt={previewItem.name}
                  style={{
                    maxWidth: '100%',
                    maxHeight: '60vh',
                    objectFit: 'contain',
                    borderRadius: 8,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  }}
                />
              ) : previewItem.mimeType.startsWith('text/') || previewItem.mimeType.includes('json') ? (
                <Box
                  sx={{
                    width: '100%',
                    height: '50vh',
                    border: '1px solid #e0e0e0',
                    borderRadius: 2,
                    overflow: 'hidden',
                  }}
                >
                  <iframe
                    src={fileApi.previewFile(previewItem.id)}
                    style={{
                      width: '100%',
                      height: '100%',
                      border: 'none',
                      background: '#f8f9fa',
                    }}
                    title={previewItem.name}
                  />
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Box sx={{ fontSize: 64, mb: 2 }}>
                    {getFileTypeIcon(previewItem.mimeType)}
                  </Box>
                  <Typography variant="h6" gutterBottom>
                    {previewItem.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Size: {formatFileSize(previewItem.size)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Type: {previewItem.mimeType}
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, background: '#f8f9fa' }}>
          <Button
            onClick={() => {
              if (previewItem) {
                const downloadUrl = fileApi.downloadFile(previewItem.id);
                downloadFile(downloadUrl, previewItem.name);
              }
            }}
            variant="contained"
            startIcon={<DownloadIcon />}
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              }
            }}
          >
            Download
          </Button>
          <Button onClick={() => setPreviewOpen(false)} variant="outlined">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FileGrid;
