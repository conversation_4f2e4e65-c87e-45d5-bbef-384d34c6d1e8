import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Chip,
} from '@mui/material';
import {
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { Item, FileItem, FolderItem } from '../types';
import { formatFileSize, formatDate, getFileIcon, isImageFile, downloadFile } from '../utils/helpers';
import { fileApi } from '../services/api';

interface FileGridProps {
  items: Item[];
  onItemClick: (item: Item) => void;
  onItemRename: (item: Item) => void;
  onItemDelete: (item: Item) => void;
}

const FileGrid: React.FC<FileGridProps> = ({
  items,
  onItemClick,
  onItemRename,
  onItemDelete,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, item: Item) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedItem(item);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedItem(null);
  };

  const handleDownload = () => {
    if (selectedItem && selectedItem.type === 'file') {
      const fileItem = selectedItem as FileItem;
      const downloadUrl = fileApi.downloadFile(fileItem.id);
      downloadFile(downloadUrl, fileItem.name);
    }
    handleMenuClose();
  };

  const handleRename = () => {
    if (selectedItem) {
      onItemRename(selectedItem);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedItem) {
      onItemDelete(selectedItem);
    }
    handleMenuClose();
  };

  const renderItemIcon = (item: Item) => {
    if (item.type === 'folder') {
      return <FolderIcon sx={{ fontSize: 48, color: 'primary.main' }} />;
    }

    const fileItem = item as FileItem;
    if (isImageFile(fileItem.mimeType)) {
      return (
        <Box
          sx={{
            width: 48,
            height: 48,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 24,
          }}
        >
          {getFileIcon(fileItem.mimeType)}
        </Box>
      );
    }

    return <FileIcon sx={{ fontSize: 48, color: 'text.secondary' }} />;
  };

  const renderItemDetails = (item: Item) => {
    if (item.type === 'folder') {
      const folderItem = item as FolderItem;
      return (
        <Typography variant="caption" color="text.secondary">
          Created: {formatDate(folderItem.createdAt)}
        </Typography>
      );
    }

    const fileItem = item as FileItem;
    return (
      <Box>
        <Typography variant="caption" color="text.secondary" display="block">
          Size: {formatFileSize(fileItem.size)}
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block">
          Uploaded: {formatDate(fileItem.uploadDate)}
        </Typography>
        <Chip
          label={fileItem.mimeType}
          size="small"
          variant="outlined"
          sx={{ mt: 0.5, fontSize: '0.7rem', height: 20 }}
        />
      </Box>
    );
  };

  if (items.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 200,
          color: 'text.secondary',
        }}
      >
        <Typography variant="h6">No files or folders found</Typography>
      </Box>
    );
  }

  return (
    <>
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: 2, p: 2 }}>
        {items.map((item) => (
          <Box key={item.id}>
            <Card
              sx={{
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 4,
                },
              }}
              onClick={() => onItemClick(item)}
            >
              <CardContent sx={{ textAlign: 'center', pb: 1 }}>
                {renderItemIcon(item)}
                <Typography
                  variant="subtitle2"
                  sx={{
                    mt: 1,
                    fontWeight: 'medium',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                  title={item.name}
                >
                  {item.name}
                </Typography>
                {renderItemDetails(item)}
              </CardContent>
              <CardActions sx={{ justifyContent: 'flex-end', pt: 0 }}>
                <IconButton
                  size="small"
                  onClick={(e) => handleMenuOpen(e, item)}
                >
                  <MoreVertIcon />
                </IconButton>
              </CardActions>
            </Card>
          </Box>
        ))}
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedItem?.type === 'file' && (
          <MenuItem onClick={handleDownload}>
            <DownloadIcon sx={{ mr: 1 }} />
            Download
          </MenuItem>
        )}
        <MenuItem onClick={handleRename}>
          <EditIcon sx={{ mr: 1 }} />
          Rename
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </>
  );
};

export default FileGrid;
