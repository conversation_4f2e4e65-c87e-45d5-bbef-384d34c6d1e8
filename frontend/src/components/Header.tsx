import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Box,
} from '@mui/material';
import {
  Search as SearchIcon,
  CloudUpload as UploadIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { useApp } from '../contexts/AppContext';
import { fileApi } from '../services/api';

interface HeaderProps {
  onUploadClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ onUploadClick }) => {
  const { state, dispatch } = useApp();
  const [searchInput, setSearchInput] = useState(state.searchQuery);

  const handleSearch = async (query: string) => {
    if (query.trim().length < 2) {
      dispatch({ type: 'CLEAR_SEARCH' });
      return;
    }

    dispatch({ type: 'SET_SEARCHING', payload: true });
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });

    try {
      const response = await fileApi.search(query);
      if (response.code === 200 && response.data) {
        const allItems = [...response.data.folders, ...response.data.files];
        dispatch({ type: 'SET_SEARCH_RESULTS', payload: allItems });
      }
    } catch (error) {
      console.error('Search error:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Search failed' });
    } finally {
      dispatch({ type: 'SET_SEARCHING', payload: false });
    }
  };

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchInput(value);

    // Debounce search
    const timeoutId = setTimeout(() => {
      handleSearch(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleClearSearch = () => {
    setSearchInput('');
    dispatch({ type: 'CLEAR_SEARCH' });
  };

  return (
    <AppBar
      position="static"
      elevation={0}
      sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderBottom: '1px solid rgba(255,255,255,0.1)',
      }}
    >
      <Toolbar sx={{ py: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              background: 'rgba(255,255,255,0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
              fontSize: '1.5rem',
            }}
          >
            📁
          </Box>
          <Typography
            variant="h5"
            component="div"
            sx={{
              fontWeight: 700,
              background: 'linear-gradient(45deg, #fff 30%, #f0f0f0 90%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Telegram File Storage
          </Typography>
        </Box>

        <Box sx={{ flexGrow: 1, maxWidth: 600, mx: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search files and folders..."
            value={searchInput}
            onChange={handleSearchInputChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: searchInput && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch}>
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                backdropFilter: 'blur(10px)',
                borderRadius: 3,
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.25)',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  border: '1px solid rgba(255, 255, 255, 0.5)',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  border: '2px solid rgba(255, 255, 255, 0.8)',
                },
                '& input': {
                  color: 'white',
                  '&::placeholder': {
                    color: 'rgba(255, 255, 255, 0.7)',
                  },
                },
                '& .MuiSvgIcon-root': {
                  color: 'rgba(255, 255, 255, 0.8)',
                },
              },
            }}
          />
        </Box>

        <Button
          variant="contained"
          startIcon={<UploadIcon />}
          onClick={onUploadClick}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: 3,
            px: 3,
            py: 1,
            fontWeight: 600,
            textTransform: 'none',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
              transform: 'translateY(-1px)',
              boxShadow: '0 4px 20px rgba(0,0,0,0.2)',
            },
            transition: 'all 0.3s ease',
          }}
        >
          Upload Files
        </Button>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
