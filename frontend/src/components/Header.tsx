import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Box,
} from '@mui/material';
import {
  Search as SearchIcon,
  CloudUpload as UploadIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { useApp } from '../contexts/AppContext';
import { fileApi } from '../services/api';

interface HeaderProps {
  onUploadClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ onUploadClick }) => {
  const { state, dispatch } = useApp();
  const [searchInput, setSearchInput] = useState(state.searchQuery);

  const handleSearch = async (query: string) => {
    if (query.trim().length < 2) {
      dispatch({ type: 'CLEAR_SEARCH' });
      return;
    }

    dispatch({ type: 'SET_SEARCHING', payload: true });
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });

    try {
      const response = await fileApi.search(query);
      if (response.code === 200 && response.data) {
        const allItems = [...response.data.folders, ...response.data.files];
        dispatch({ type: 'SET_SEARCH_RESULTS', payload: allItems });
      }
    } catch (error) {
      console.error('Search error:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Search failed' });
    } finally {
      dispatch({ type: 'SET_SEARCHING', payload: false });
    }
  };

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchInput(value);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      handleSearch(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleClearSearch = () => {
    setSearchInput('');
    dispatch({ type: 'CLEAR_SEARCH' });
  };

  return (
    <AppBar position="static" elevation={1}>
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 0, mr: 3 }}>
          TeleStore
        </Typography>

        <Box sx={{ flexGrow: 1, maxWidth: 600, mx: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search files and folders..."
            value={searchInput}
            onChange={handleSearchInputChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: searchInput && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch}>
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.25)',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
              },
            }}
          />
        </Box>

        <Button
          variant="contained"
          startIcon={<UploadIcon />}
          onClick={onUploadClick}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
            },
          }}
        >
          Upload
        </Button>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
