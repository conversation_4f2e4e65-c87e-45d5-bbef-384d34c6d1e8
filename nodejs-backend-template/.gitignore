# Dependencies
node_modules/
package-lock.json
yarn.lock

# Logs
logs/*
!logs/.gitkeep
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config/

# Directory for uploaded files
public/uploads/*
!public/uploads/.gitkeep
public/downloads/*
!public/downloads/.gitkeep

# IDE
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
/sync*.js
lib/script/
