{"name": "nodejs-backend-template", "version": "1.0.0", "description": "A clean Node.js backend template with MongoDB and Redis", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "license": "MIT", "dependencies": {"async": "^3.2.4", "bcryptjs": "^2.4.3", "config": "^3.3.9", "cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^7.5.0", "ms": "^2.1.3", "nodemailer": "^6.9.5", "redis": "^4.6.8", "socket.io": "^4.7.2", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"nodemon": "^3.0.1"}}